import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UsersService } from '../../users/users.service';
import { UserRole } from '../../users/entities/user.entity';

@Injectable()
export class AdminSeederService implements OnModuleInit {
  private readonly logger = new Logger(AdminSeederService.name);

  constructor(
    private readonly usersService: UsersService,
    private readonly configService: ConfigService,
  ) {}

  async onModuleInit() {
    await this.createDefaultAdmin();
  }

  private async createDefaultAdmin() {
    try {
      // Verificar si ya existen usuarios en la base de datos
      const users = await this.usersService.findAll();
      
      if (users.length === 0) {
        this.logger.log('No users found in database. Creating default admin user...');
        
        const defaultAdmin = {
          email: this.configService.get<string>('ADMIN_EMAIL') || '<EMAIL>',
          firstName: 'Super',
          lastName: 'Admin',
          password: this.configService.get<string>('ADMIN_PASSWORD') || 'Admin123',
          role: UserRole.ADMIN,
        };

        const adminUser = await this.usersService.create(defaultAdmin);
        
        this.logger.log(`Default admin user created successfully with email: ${adminUser.email}`);
        this.logger.warn('⚠️  IMPORTANT: Please change the default admin password after first login!');
        this.logger.log('📧 Default admin credentials:');
        this.logger.log(`   Email: ${defaultAdmin.email}`);
        this.logger.log(`   Password: ${defaultAdmin.password}`);
      } else {
        this.logger.log(`Database already contains ${users.length} user(s). Skipping admin creation.`);
      }
    } catch (error) {
      this.logger.error('Failed to create default admin user:', error.message);
    }
  }
}
