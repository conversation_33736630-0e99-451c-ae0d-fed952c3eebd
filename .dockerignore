# Dependencias
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Archivos de build
dist
build
*.tsbuildinfo

# Archivos de desarrollo
.env
.env.local
.env.development
.env.test
.env.production

# Logs
logs
*.log

# Coverage directory usado por herramientas como istanbul
coverage
*.lcov

# Archivos temporales
.tmp
.temp

# IDE y editores
.vscode
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore
.gitattributes

# Docker
Dockerfile*
.dockerignore
docker-compose*.yml

# Testing
# test - Comentado para permitir archivos de configuración de test si son necesarios
*.spec.ts
*.test.ts
jest.config.js
.nyc_output

# Documentation
README.md
docs

# Otros archivos de configuración de desarrollo
.eslintrc*
.prettierrc*
# tsconfig.json - NECESARIO para build
# tsconfig.build.json - NECESARIO para build
# nest-cli.json - NECESARIO para build
