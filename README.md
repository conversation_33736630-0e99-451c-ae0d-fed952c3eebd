# SQQ API - Sistema de Autenticación y Gestión de Llaves Cuánticas

API completa de autenticación, gestión de usuarios e integración con SeQRNG para generación de llaves cuánticas desarrollada con NestJS, PostgreSQL y JWT.

## 🚀 Características

- ✅ **Autenticación JWT** completa con access y refresh tokens
- ✅ **Registro y login** de usuarios
- ✅ **Gestión de usuarios** con roles (USER/ADMIN)
- ✅ **Configuración CTM por usuario** para acceso personalizado a CipherTrust Manager
- ✅ **Generación de llaves cuánticas** usando SeQRNG
- ✅ **Gestión de llaves en CTM** (subida individual y por lotes)
- ✅ **Cifrado seguro** de credenciales CTM
- ✅ **Base de datos PostgreSQL** con TypeORM
- ✅ **Validación robusta** con class-validator
- ✅ **Documentación Swagger** interactiva
- ✅ **Tests unitarios** completos
- ✅ **Arquitectura escalable** y bien estructurada
- ✅ **Manejo de errores** centralizado
- ✅ **Seguridad** con bcrypt y guards

## 📋 Requisitos Previos

- Node.js 20+ (usar `nvm use 20`)
- PostgreSQL 12+
- npm o yarn

## 🛠️ Instalación

1. **Clonar el repositorio**
```bash
git clone <repository-url>
cd sqq-api
```

2. **Usar Node.js 20**
```bash
nvm use 20
```

3. **Instalar dependencias**
```bash
npm install
```

4. **Configurar variables de entorno**
```bash
cp .env.example .env
```

Editar `.env` con tus configuraciones:
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=tu_password
DB_DATABASE=sqq_db

# JWT Configuration
JWT_SECRET=tu-super-secreto-jwt-key
JWT_EXPIRES_IN=1h
JWT_REFRESH_SECRET=tu-super-secreto-refresh-key
JWT_REFRESH_EXPIRES_IN=7d

# Application Configuration
PORT=3000
NODE_ENV=development

# Security
BCRYPT_ROUNDS=12

# SeQRNG API Configuration
SEQRNG_API_URL=http://localhost:5000
```

5. **Crear la base de datos**
```sql
CREATE DATABASE sqq_db;
```

## 🚀 Ejecución

```bash
# Desarrollo con hot reload
npm run start:dev

# Producción
npm run start:prod

# Modo debug
npm run start:debug
```

La aplicación estará disponible en:
- **API**: http://localhost:3000
- **Swagger**: http://localhost:3000/api

## 📚 Documentación API

### Endpoints de Autenticación

#### POST /auth/register
Registrar nuevo usuario
```json
{
  "email": "<EMAIL>",
  "firstName": "Juan",
  "lastName": "Pérez",
  "password": "MiPassword123!"
}
```

#### POST /auth/login
Iniciar sesión
```json
{
  "email": "<EMAIL>",
  "password": "MiPassword123!"
}
```

#### POST /auth/refresh
Renovar tokens
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### POST /auth/logout
Cerrar sesión (requiere autenticación)

#### GET /auth/profile
Obtener perfil del usuario (requiere autenticación)

### Endpoints de Usuarios

#### GET /users
Listar todos los usuarios (solo ADMIN)

#### GET /users/:id
Obtener usuario por ID

#### POST /users
Crear usuario (solo ADMIN)
```json
{
  "email": "<EMAIL>",
  "firstName": "Juan",
  "lastName": "Pérez",
  "company": "Acme Corporation",
  "password": "MiPassword123!",
  "role": "USER",
  "ctmIpAddress": "https://ctm.example.com:443",
  "ctmUsername": "ctm_admin",
  "ctmPassword": "ctm_password123",
  "ctmDomain": "root"
}
```

#### PATCH /users/:id
Actualizar usuario

#### DELETE /users/:id
Eliminar usuario (solo ADMIN)

### Endpoints de Generación de Llaves Cuánticas

#### POST /keys/generate/bytes
Generar bytes aleatorios cuánticos
```json
{
  "num_bytes": 32,
  "packages": 1
}
```

#### POST /keys/generate/hex
Generar llave hexadecimal cuántica
```json
{
  "num_bytes": 32
}
```

#### POST /keys/generate/alphanumeric
Generar llave alfanumérica cuántica
```json
{
  "num_bytes": 32
}
```

#### POST /keys/upload-to-ctm
Subir llave a CipherTrust Manager (el owner se determina automáticamente)
```json
{
  "key_name": "my-encryption-key-001",
  "algorithm": "AES",
  "num_bytes": 32,
  "exportable": false,
  "key_material_base64": "optional-base64-key"
}
```
**Nota:** El propietario (owner) se determina automáticamente:
- Si el usuario tiene empresa: usa el nombre de la empresa (convertido a minúsculas y espacios reemplazados por `_`)
- Si no tiene empresa: usa el formato `firstName_lastName` (en minúsculas)

**Ejemplos:**
- Empresa "Acme Corporation" → owner: `acme_corporation`
- Usuario "Juan Pérez" sin empresa → owner: `juan_pérez`

#### POST /keys/upload-batch-to-ctm
Subir múltiples llaves a CTM (el owner se determina automáticamente)
```json
{
  "key_name_prefix": "batch-key",
  "algorithm": "AES",
  "num_bytes": 32,
  "key_count": 5,
  "exportable": false
}
```
**Nota:** El propietario (owner) se determina automáticamente igual que en el endpoint individual.

#### GET /keys/ctm/:keyName/exists
Verificar si una llave existe en CTM

#### GET /keys/ctm/auth/token
Obtener token de autenticación CTM

## 🧪 Testing

```bash
# Tests unitarios
npm run test

# Tests con coverage
npm run test:cov

# Tests en modo watch
npm run test:watch

# Tests e2e
npm run test:e2e
```

## 🏗️ Arquitectura

```
src/
├── auth/                 # Módulo de autenticación
│   ├── decorators/      # Decoradores personalizados
│   ├── dto/             # DTOs de autenticación
│   ├── guards/          # Guards de seguridad
│   ├── interfaces/      # Interfaces TypeScript
│   └── strategies/      # Estrategias Passport
├── common/              # Código compartido
│   ├── filters/         # Filtros de excepción
│   ├── pipes/           # Pipes de validación
│   └── services/        # Servicios compartidos
│       ├── encryption.service.ts    # Cifrado de credenciales CTM
│       └── seqrng-client.service.ts # Cliente para API SeQRNG
├── config/              # Configuración de la app
├── database/            # Configuración de base de datos
├── keys/                # Módulo de gestión de llaves
│   ├── dto/             # DTOs para generación de llaves
│   ├── keys.controller.ts # Controlador de llaves
│   └── keys.service.ts  # Servicio de llaves
└── users/               # Módulo de usuarios
    ├── dto/             # DTOs de usuarios
    └── entities/        # Entidades TypeORM
```

### Integración con SeQRNG

La aplicación se integra con la API Flask de SeQRNG para:

1. **Generación de llaves cuánticas**: Utiliza el generador cuántico SeQRNG
2. **Gestión en CTM**: Sube las llaves generadas a CipherTrust Manager
3. **Configuración por usuario**: Cada usuario tiene su propia configuración CTM
4. **Seguridad**: Las credenciales CTM se cifran antes de almacenarse

### Flujo de Trabajo

1. **Admin crea usuario** → Incluye configuración CTM personalizada
2. **Usuario se autentica** → Obtiene JWT con información de usuario
3. **Usuario genera llaves** → API usa configuración CTM del usuario
4. **Llaves se suben a CTM** → Usando credenciales específicas del usuario

## 🔐 Seguridad

- **Passwords**: Hasheados con bcrypt (12 rounds)
- **JWT**: Tokens seguros con expiración
- **Refresh Tokens**: Almacenados hasheados en BD
- **Validación**: Validación estricta de entrada
- **Guards**: Protección de rutas por roles
- **CORS**: Configurado para desarrollo/producción

## 🚀 Despliegue

### Variables de Entorno de Producción
```env
NODE_ENV=production
JWT_SECRET=clave-super-secreta-produccion
JWT_REFRESH_SECRET=clave-refresh-super-secreta-produccion
DB_HOST=tu-host-produccion
# ... otras variables
```

## 🤝 Contribución

1. Fork el proyecto
2. Crear rama feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

## 📝 Licencia

PENDIENTE