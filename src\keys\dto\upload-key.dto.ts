import { IsString, IsOptional, <PERSON>I<PERSON>, <PERSON>, <PERSON>, IsBoolean, IsEnum } from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export enum KeyAlgorithm {
  AES = 'aes',
  ARIA = 'aria',
  RSA = 'rsa',
  HMAC_SHA1 = 'hmac-sha1',
  HMAC_SHA256 = 'hmac-sha256',
  HMAC_SHA384 = 'hmac-sha384',
  HMAC_SHA512 = 'hmac-sha512',
}

export class UploadKeyDto {
  @ApiProperty({
    example: 'my-encryption-key-001',
    description: 'Nombre de la llave en CTM',
  })
  @IsString()
  key_name: string;

  @ApiProperty({
    example: KeyAlgorithm.AES,
    description: 'Algoritmo de la llave',
    enum: KeyAlgorithm,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => value ? value.toLowerCase() : value)
  @IsEnum(KeyAlgorithm)
  algorithm?: KeyAlgorithm = KeyAlgorithm.AES;

  @ApiProperty({
    example: 32,
    description: 'Número de bytes para la llave',
    minimum: 1,
    maximum: 1024,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(1024)
  num_bytes?: number = 32;

  // owner se determina automáticamente basado en el usuario autenticado

  @ApiProperty({
    example: false,
    description: 'Si la llave es exportable',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  exportable?: boolean = false;

  @ApiProperty({
    example: 'base64-encoded-key-material',
    description: 'Material de llave en base64 (opcional, se genera si no se proporciona)',
    required: false,
  })
  @IsOptional()
  @IsString()
  key_material_base64?: string;
}
