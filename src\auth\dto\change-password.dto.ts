import { IsString, Min<PERSON>ength, <PERSON><PERSON>ength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ChangePasswordDto {
  @ApiProperty({
    example: 'MiPasswordActual123!',
    description: 'Contraseña actual del usuario',
  })
  @IsString()
  @MinLength(1)
  currentPassword: string;

  @ApiProperty({
    example: 'MiNuevaPassword123!',
    description: 'Nueva contraseña del usuario',
    minLength: 8,
    maxLength: 128,
  })
  @IsString()
  @MinLength(8)
  @MaxLength(128)
  newPassword: string;

  @ApiProperty({
    example: 'MiNuevaPassword123!',
    description: 'Confirmación de la nueva contraseña',
    minLength: 8,
    maxLength: 128,
  })
  @IsString()
  @MinLength(8)
  @MaxLength(128)
  confirmPassword: string;
}
