import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { AxiosResponse } from 'axios';

export interface CtmConfig {
  ipAddress: string;
  username: string;
  password: string;
  domain: string;
}

export interface SeqrngConfig {
  ipAddress: string;
  apiToken: string;
}

export interface GenerateKeyRequest {
  num_bytes?: number;
  packages?: number;
}

export interface GenerateHexKeyRequest {
  num_bytes?: number;
}

export interface GenerateAlphanumericKeyRequest {
  num_bytes?: number;
}

export interface UploadKeyRequest {
  key_name: string;
  algorithm?: string;
  num_bytes?: number;
  owner?: string;
  exportable?: boolean;
  key_material_base64?: string;
}

export interface UploadBatchKeyRequest {
  key_name_prefix: string;
  algorithm?: string;
  num_bytes?: number;
  owner?: string;
  exportable?: boolean;
  key_count?: number;
  key_num_start?: number;
}

export interface SeqrngApiResponse<T = any> {
  status: string;
  message: string;
  data: T;
  timestamp: string;
}

@Injectable()
export class SeqrngClientService {
  private readonly logger = new Logger(SeqrngClientService.name);
  private readonly baseUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    // Get SeQRNG API base URL from configuration
    this.baseUrl = this.configService.get<string>('seqrng.apiUrl', 'http://localhost:5000');
  }

  /**
   * Generate random bytes from SeQRNG
   */
  async generateRandomBytes(
    request: GenerateKeyRequest,
    ctmConfig?: CtmConfig,
    seqrngConfig?: SeqrngConfig,
  ): Promise<SeqrngApiResponse> {
    try {
      // Use dynamic endpoint if CTM config is provided
      const url = ctmConfig
        ? `${this.baseUrl}/api/v1/keys/generate/bytes/dynamic`
        : `${this.baseUrl}/api/v1/keys/generate/bytes`;
      const payload = this.buildPayload(request, ctmConfig, seqrngConfig);

      const response: AxiosResponse<SeqrngApiResponse> = await firstValueFrom(
        this.httpService.post(url, payload),
      );

      return response.data;
    } catch (error) {
      this.logger.error('Failed to generate random bytes', error);
      throw new Error(`SeQRNG API error: ${error.message}`);
    }
  }

  /**
   * Generate hexadecimal key from SeQRNG
   */
  async generateHexKey(
    request: GenerateHexKeyRequest,
    ctmConfig?: CtmConfig,
    seqrngConfig?: SeqrngConfig,
  ): Promise<SeqrngApiResponse> {
    try {
      // Use dynamic endpoint if CTM config is provided
      const url = ctmConfig
        ? `${this.baseUrl}/api/v1/keys/generate/hex/dynamic`
        : `${this.baseUrl}/api/v1/keys/generate/hex`;
      const payload = this.buildPayload(request, ctmConfig, seqrngConfig);

      const response: AxiosResponse<SeqrngApiResponse> = await firstValueFrom(
        this.httpService.post(url, payload),
      );

      return response.data;
    } catch (error) {
      this.logger.error('Failed to generate hex key', error);
      throw new Error(`SeQRNG API error: ${error.message}`);
    }
  }

  /**
   * Generate alphanumeric key from SeQRNG
   */
  async generateAlphanumericKey(
    request: GenerateAlphanumericKeyRequest,
    ctmConfig?: CtmConfig,
    seqrngConfig?: SeqrngConfig,
  ): Promise<SeqrngApiResponse> {
    try {
      // Use dynamic endpoint if CTM config is provided
      const url = ctmConfig
        ? `${this.baseUrl}/api/v1/keys/generate/alphanumeric/dynamic`
        : `${this.baseUrl}/api/v1/keys/generate/alphanumeric`;
      const payload = this.buildPayload(request, ctmConfig, seqrngConfig);

      const response: AxiosResponse<SeqrngApiResponse> = await firstValueFrom(
        this.httpService.post(url, payload),
      );

      return response.data;
    } catch (error) {
      this.logger.error('Failed to generate alphanumeric key', error);
      throw new Error(`SeQRNG API error: ${error.message}`);
    }
  }

  /**
   * Upload single key to CTM
   */
  async uploadKeyToCtm(
    request: UploadKeyRequest,
    ctmConfig: CtmConfig,
    seqrngConfig?: SeqrngConfig,
  ): Promise<SeqrngApiResponse> {
    try {
      const url = `${this.baseUrl}/api/v1/ctm/keys/upload/dynamic`;
      const payload = this.buildPayload(request, ctmConfig, seqrngConfig);

      const response: AxiosResponse<SeqrngApiResponse> = await firstValueFrom(
        this.httpService.post(url, payload),
      );

      return response.data;
    } catch (error) {
      this.logger.error('Failed to upload key to CTM', error);
      throw new Error(`SeQRNG API error: ${error.message}`);
    }
  }

  /**
   * Upload multiple keys to CTM
   */
  async uploadBatchKeysToCtm(
    request: UploadBatchKeyRequest,
    ctmConfig: CtmConfig,
    seqrngConfig?: SeqrngConfig,
  ): Promise<SeqrngApiResponse> {
    try {
      const url = `${this.baseUrl}/api/v1/ctm/keys/upload/batch/dynamic`;
      const payload = this.buildPayload(request, ctmConfig, seqrngConfig);

      const response: AxiosResponse<SeqrngApiResponse> = await firstValueFrom(
        this.httpService.post(url, payload),
      );

      return response.data;
    } catch (error) {
      this.logger.error('Failed to upload batch keys to CTM', error);
      throw new Error(`SeQRNG API error: ${error.message}`);
    }
  }

  /**
   * Check if key exists in CTM
   */
  async checkKeyExists(
    keyName: string,
    ctmConfig: CtmConfig,
    seqrngConfig?: SeqrngConfig,
  ): Promise<SeqrngApiResponse> {
    try {
      const url = `${this.baseUrl}/api/v1/ctm/keys/${keyName}/exists/dynamic`;
      const payload = this.buildPayload({}, ctmConfig, seqrngConfig);

      const response: AxiosResponse<SeqrngApiResponse> = await firstValueFrom(
        this.httpService.post(url, payload),
      );

      return response.data;
    } catch (error) {
      this.logger.error('Failed to check key existence', error);
      throw new Error(`SeQRNG API error: ${error.message}`);
    }
  }

  /**
   * Get CTM authentication token
   */
  async getCtmToken(ctmConfig: CtmConfig, seqrngConfig?: SeqrngConfig): Promise<SeqrngApiResponse> {
    try {
      const url = `${this.baseUrl}/api/v1/ctm/auth/token/dynamic`;
      const payload = this.buildPayload({}, ctmConfig, seqrngConfig);

      const response: AxiosResponse<SeqrngApiResponse> = await firstValueFrom(
        this.httpService.post(url, payload),
      );

      return response.data;
    } catch (error) {
      this.logger.error('Failed to get CTM token', error);
      throw new Error(`SeQRNG API error: ${error.message}`);
    }
  }

  /**
   * Build request payload with CTM and SeqRNG configuration
   */
  private buildPayload(request: any, ctmConfig?: CtmConfig, seqrngConfig?: SeqrngConfig): any {
    const payload = { ...request };

    if (ctmConfig) {
      payload.ctm_config = {
        ip_address: ctmConfig.ipAddress,
        username: ctmConfig.username,
        password: ctmConfig.password,
        domain: ctmConfig.domain,
      };
    }

    if (seqrngConfig) {
      payload.seqrng_config = {
        ip_address: seqrngConfig.ipAddress,
        api_token: seqrngConfig.apiToken,
      };
    }

    return payload;
  }


}
