#!/bin/sh

# Docker entrypoint script for NestJS API
# This script runs database migrations before starting the application

set -e

echo "🚀 Starting NestJS API container..."

# Function to wait for database
wait_for_db() {
  echo "⏳ Waiting for database to be ready..."
  
  until node -e "
    const { Client } = require('pg');
    const client = new Client({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      user: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_DATABASE,
    });
    client.connect()
      .then(() => {
        console.log('✅ Database connection successful');
        client.end();
        process.exit(0);
      })
      .catch((err) => {
        console.log('❌ Database connection failed:', err.message);
        process.exit(1);
      });
  "; do
    echo "⏳ Database not ready yet, waiting 5 seconds..."
    sleep 5
  done
}

# Function to run migrations
run_migrations() {
  echo "🔄 Running database migrations..."
  
  # Check if TypeORM CLI is available
  if [ -f "node_modules/.bin/typeorm" ]; then
    echo "📦 Running TypeORM migrations..."
    npm run migration:run || {
      echo "⚠️ TypeORM migrations failed, but continuing..."
    }
  else
    echo "⚠️ TypeORM CLI not found, skipping migrations"
  fi
  
  echo "✅ Migration process completed"
}

# Function to start the application
start_app() {
  echo "🎯 Starting NestJS application..."
  exec "$@"
}

# Main execution flow
main() {
  # Wait for database to be available
  wait_for_db
  
  # Run migrations
  run_migrations
  
  # Start the application
  start_app "$@"
}

# Execute main function with all arguments
main "$@"
