import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateAlgorithmEnumValues1753119545861 implements MigrationInterface {
    name = 'UpdateAlgorithmEnumValues1753119545861'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // First, change the column to text temporarily
        await queryRunner.query(`ALTER TABLE "keys" ALTER COLUMN "algorithm" TYPE text`);

        // Update existing data to match new enum values
        await queryRunner.query(`
          UPDATE "keys"
          SET "algorithm" = 'aes'
          WHERE "algorithm" = 'AES'
        `);

        await queryRunner.query(`
          UPDATE "keys"
          SET "algorithm" = 'aria'
          WHERE "algorithm" = 'ARIA'
        `);

        // Update HMAC to hmac-sha256 as default
        await queryRunner.query(`
          UPDATE "keys"
          SET "algorithm" = 'hmac-sha256'
          WHERE "algorithm" = 'HMAC'
        `);

        // Now update the enum
        await queryRunner.query(`DROP TYPE "public"."keys_algorithm_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."keys_algorithm_enum" AS ENUM('aes', 'aria', 'RSA', 'hmac-sha1', 'hmac-sha256', 'hmac-sha384', 'hmac-sha512')`);
        await queryRunner.query(`ALTER TABLE "keys" ALTER COLUMN "algorithm" TYPE "public"."keys_algorithm_enum" USING "algorithm"::"text"::"public"."keys_algorithm_enum"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Change column to text temporarily
        await queryRunner.query(`ALTER TABLE "keys" ALTER COLUMN "algorithm" TYPE text`);

        // Revert data changes
        await queryRunner.query(`
          UPDATE "keys"
          SET "algorithm" = 'AES'
          WHERE "algorithm" = 'aes'
        `);

        await queryRunner.query(`
          UPDATE "keys"
          SET "algorithm" = 'ARIA'
          WHERE "algorithm" = 'aria'
        `);

        // Revert specific HMAC values back to generic HMAC
        await queryRunner.query(`
          UPDATE "keys"
          SET "algorithm" = 'HMAC'
          WHERE "algorithm" IN ('hmac-sha1', 'hmac-sha256', 'hmac-sha384', 'hmac-sha512')
        `);

        // Revert enum changes
        await queryRunner.query(`DROP TYPE "public"."keys_algorithm_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."keys_algorithm_enum" AS ENUM('AES', 'RSA', 'HMAC')`);
        await queryRunner.query(`ALTER TABLE "keys" ALTER COLUMN "algorithm" TYPE "public"."keys_algorithm_enum" USING "algorithm"::"text"::"public"."keys_algorithm_enum"`);
    }

}
